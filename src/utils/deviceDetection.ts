/**
 * Utility functions for device detection and app store redirection
 */

// App store URLs
export const APP_STORE_URLS = {
  ios: 'https://apps.apple.com/br/app/id6748916020',
  android: 'https://play.google.com/store/apps/details?id=br.com.izymercado.app&pli=1'
} as const;

/**
 * Detects if the user is on an iOS device
 */
export function isIOS(): boolean {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent.toLowerCase();
  const platform = navigator.platform?.toLowerCase() || '';

  // Check for iOS devices
  return /ipad|iphone|ipod/.test(userAgent) ||
         (platform === 'macintel' && navigator.maxTouchPoints > 1) ||
         /ios/.test(userAgent);
}

/**
 * Detects if the user is on an Android device
 */
export function isAndroid(): boolean {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent.toLowerCase();
  return /android/.test(userAgent);
}

/**
 * Gets the appropriate app store URL based on the user's device
 */
export function getAppStoreUrl(): string {
  if (isIOS()) {
    return APP_STORE_URLS.ios;
  } else if (isAndroid()) {
    return APP_STORE_URLS.android;
  } else {
    // Default to Android for desktop/other devices
    return APP_STORE_URLS.android;
  }
}

/**
 * Redirects the user to the appropriate app store
 */
export function redirectToAppStore(): void {
  const url = getAppStoreUrl();
  window.open(url, '_blank', 'noopener,noreferrer');
}
